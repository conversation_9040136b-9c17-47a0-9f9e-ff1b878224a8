{"version": 3, "file": "split-panel2.mjs", "sources": ["../../../../../../packages/components/splitter/src/split-panel.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type SplitterPanel from './split-panel.vue'\n\nexport const splitterPanelProps = buildProps({\n  min: {\n    type: [String, Number],\n  },\n  max: {\n    type: [String, Number],\n  },\n  size: {\n    type: [String, Number],\n  },\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  collapsible: Boolean,\n} as const)\n\nexport type SplitterPanelProps = ExtractPropTypes<typeof splitterPanelProps>\nexport type SplitterPanelPropsPublic = __ExtractPublicPropTypes<\n  typeof splitterPanelProps\n>\nexport type SplitterPanelInstance = InstanceType<typeof SplitterPanel> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,kBAAkB,GAAG,UAAU,CAAC;AAC7C,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE,OAAO;AACtB,CAAC;;;;"}