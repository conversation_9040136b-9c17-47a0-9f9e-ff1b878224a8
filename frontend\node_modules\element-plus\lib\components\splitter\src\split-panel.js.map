{"version": 3, "file": "split-panel.js", "sources": ["../../../../../../packages/components/splitter/src/split-panel.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  reactive,\n  ref,\n  toRefs,\n  watch,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { throwError } from '@element-plus/utils'\nimport { getCollapsible, isCollapsible } from './hooks/usePanel'\nimport SplitBar from './split-bar.vue'\nimport { splitterPanelProps } from './split-panel'\nimport { getPct, getPx, isPct, isPx } from './hooks'\nimport { splitterRootContextKey } from './type'\n\nconst ns = useNamespace('splitter-panel')\n\nconst COMPONENT_NAME = 'ElSplitterPanel'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(splitterPanelProps)\n\nconst emits = defineEmits<{\n  (e: 'update:size', value: number): void\n}>()\nconst splitterContext = inject(splitterRootContextKey)\nif (!splitterContext)\n  throwError(\n    COMPONENT_NAME,\n    'usage: <el-splitter><el-splitter-panel /></el-splitter/>'\n  )\n\nconst { panels, layout, containerSize, pxSizes } = toRefs(splitterContext)\n\nconst {\n  registerPanel,\n  unregisterPanel,\n  onCollapse,\n  onMoveEnd,\n  onMoveStart,\n  onMoving,\n} = splitterContext\n\nconst panelEl = ref<HTMLDivElement>()\nconst instance = getCurrentInstance()!\nconst uid = instance.uid\n\nconst index = ref(0)\nconst panel = computed(() => panels.value[index.value])\n\nconst setIndex = (val: number) => {\n  index.value = val\n}\n\nconst panelSize = computed(() => {\n  if (!panel.value) return 0\n  return pxSizes.value[index.value] ?? 0\n})\n\nconst nextSize = computed(() => {\n  if (!panel.value) return 0\n  return pxSizes.value[index.value + 1] ?? 0\n})\n\nconst nextPanel = computed(() => {\n  if (panel.value) {\n    return panels.value[index.value + 1]\n  }\n  return null\n})\n\nconst isResizable = computed(() => {\n  if (!nextPanel.value) return false\n  return (\n    props.resizable &&\n    nextPanel.value?.resizable &&\n    // If it is 0, it means it is collapsed => check if the minimum value is set\n    (panelSize.value !== 0 || !props.min) &&\n    (nextSize.value !== 0 || !nextPanel.value.min)\n  )\n})\n\n// The last panel doesn't need a drag bar\nconst isShowBar = computed(() => {\n  if (!panel.value) return false\n  return index.value !== panels.value.length - 1\n})\n\nconst startCollapsible = computed(() =>\n  isCollapsible(panel.value, panelSize.value, nextPanel.value, nextSize.value)\n)\n\nconst endCollapsible = computed(() =>\n  isCollapsible(nextPanel.value, nextSize.value, panel.value, panelSize.value)\n)\n\nfunction sizeToPx(str: string | number | undefined) {\n  if (isPct(str)) {\n    return getPct(str) * containerSize.value || 0\n  } else if (isPx(str)) {\n    return getPx(str)\n  }\n  return str ?? 0\n}\n\n// Two-way binding for size\nlet isSizeUpdating = false\nwatch(\n  () => props.size,\n  () => {\n    if (panel.value) {\n      const size = sizeToPx(props.size)\n      const maxSize = sizeToPx(props.max)\n      const minSize = sizeToPx(props.min)\n\n      // Ensure it is within the maximum and minimum value range\n      const finalSize = Math.min(Math.max(size, minSize || 0), maxSize || size)\n\n      if (finalSize !== size) {\n        isSizeUpdating = true\n        emits('update:size', finalSize)\n      }\n\n      panel.value.size = finalSize\n      nextTick(() => (isSizeUpdating = false))\n    }\n  }\n)\n\nwatch(\n  () => panel.value?.size,\n  (val) => {\n    if (!isSizeUpdating && val !== props.size) {\n      emits('update:size', val as number)\n    }\n  }\n)\n\nwatch(\n  () => props.resizable,\n  (val) => {\n    if (panel.value) {\n      panel.value.resizable = val\n    }\n  }\n)\n\nconst _panel = reactive({\n  el: panelEl.value!,\n  uid,\n  getVnode: () => instance.vnode,\n  setIndex,\n  ...props,\n  collapsible: getCollapsible(props.collapsible),\n})\n\nregisterPanel(_panel)\n\nonBeforeUnmount(() => unregisterPanel(_panel))\n</script>\n\n<template>\n  <div\n    ref=\"panelEl\"\n    :class=\"[ns.b()]\"\n    :style=\"{ flexBasis: `${panelSize}px` }\"\n    v-bind=\"$attrs\"\n  >\n    <slot />\n  </div>\n  <SplitBar\n    v-if=\"isShowBar\"\n    :index=\"index\"\n    :layout=\"layout\"\n    :resizable=\"isResizable\"\n    :start-collapsible=\"startCollapsible\"\n    :end-collapsible=\"endCollapsible\"\n    @move-start=\"onMoveStart\"\n    @moving=\"onMoving\"\n    @move-end=\"onMoveEnd\"\n    @collapse=\"onCollapse\"\n  >\n    <template #start-collapsible>\n      <slot name=\"start-collapsible\" />\n    </template>\n    <template #end-collapsible>\n      <slot name=\"end-collapsible\" />\n    </template>\n  </SplitBar>\n</template>\n"], "names": ["useNamespace", "inject", "splitterRootContextKey", "throwError", "toRefs", "ref", "getCurrentInstance", "index", "computed", "isCollapsible", "isPct", "getPct", "isPx", "getPx", "watch", "nextTick", "reactive", "getCollapsible", "onBeforeUnmount", "_openBlock", "_createElementBlock", "_Fragment", "_createElementVNode", "_mergeProps", "_unref"], "mappings": ";;;;;;;;;;;;;;;uCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AALA,IAAM,MAAA,EAAA,GAAKA,mBAAa,gBAAgB,CAAA,CAAA;AAYxC,IAAM,MAAA,eAAA,GAAkBC,WAAOC,2BAAsB,CAAA,CAAA;AACrD,IAAA,IAAI,CAAC,eAAA;AACH,MAAAC,gBAAA,CAAA,cAAA,EAAA,0DAAA,CAAA,CAAA;AAAA,IACE,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,aAAA,EAAA,OAAA,EAAA,GAAAC,UAAA,CAAA,eAAA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACF,aAAA;AAEF,MAAA,eAAgB;AAEhB,MAAM,UAAA;AAAA,MACJ,SAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,KACA,GAAA,eAAA,CAAA;AAAA,IACA,MAAA,OAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IACA,MAAA,QAAA,GAAAC,sBAAA,EAAA,CAAA;AAAA,IACF,MAAI,GAAA,GAAA,QAAA,CAAA,GAAA,CAAA;AAEJ,IAAA,MAAMC,iBAA8B,CAAA,CAAA,CAAA,CAAA;AACpC,IAAA,MAAM,oBAA8B,CAAA,MAAA,MAAA,CAAA,KAAA,CAAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACpC,IAAA,MAAM,QAAe,GAAA,CAAA,GAAA,KAAA;AAErB,MAAMA,OAAA,CAAA,KAAA,MAAa,CAAA;AACnB,KAAA,CAAA;AAEA,IAAM,MAAA,SAAA,GAAYC,YAAgB,CAAA,MAAA;AAChC,MAAA,IAAA,EAAM,CAAQ;AAAA,MAChB,IAAA,CAAA,KAAA,CAAA,KAAA;AAEA,QAAM,OAAA,CAAA,CAAA;AACJ,MAAI,OAAO,CAAA,EAAA,GAAA,OAAc,CAAA,KAAA,CAAAD,OAAA,CAAA,KAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA;AACzB,KAAA,CAAA,CAAA;AAAqC,IACvC,MAAC,QAAA,GAAAC,YAAA,CAAA,MAAA;AAED,MAAM,IAAA,EAAA,CAAA;AACJ,MAAI,IAAA,CAAC,KAAM,CAAA,KAAA;AACX,QAAA,OAAe,CAAA,CAAA;AAA0B,MAC1C,OAAA,CAAA,EAAA,GAAA,OAAA,CAAA,KAAA,CAAAD,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAI,SAAa,GAAAC,YAAA,CAAA,MAAA;AACf,MAAA,IAAA,KAAO,CAAO,KAAA,EAAA;AAAqB,QACrC,OAAA,MAAA,CAAA,KAAA,CAAAD,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,IAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,WAAW,GAAAC,YAAc,CAAA,MAAA;AAC7B,MACE,IAAA,EAAA,CAAA;AACiB,MAEhB,IAAA,CAAA,SAAoB,CAAA,KAAA;AACqB,QAE7C,OAAA,KAAA,CAAA;AAGD,MAAM,OAAA,KAAA,CAAA,cAA2B,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,CAAA,KAAA,SAAA,CAAA,KAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,QAAA,CAAA,KAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAC/B,KAAI,CAAA,CAAA;AACJ,IAAA,MAAA,SAAa,GAAAA,YAAiB,CAAA,MAAA;AAAe,MAC9C,IAAA,CAAA,KAAA,CAAA,KAAA;AAED,QAAA,OAAyB,KAAA,CAAA;AAAA,MAAS,yBACZ,MAAA,CAAA,eAAwB,CAAA,CAAA;AAA+B,KAC7E,CAAA,CAAA;AAEA,IAAA,MAAM,gBAAiB,GAAAA,YAAA,CAAA,MAAAC,sBAAA,CAAA,KAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,EAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAAS,MAAA,iBACND,YAAA,CAAA,MAAAC,sBAAuB,CAAA,SAAM,CAAO,KAAA,EAAA,QAAA,CAAU,KAAK,EAAA,KAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC7E,SAAA,QAAA,CAAA,GAAA,EAAA;AAEA,MAAA,IAAAC,oBAAkB;AAChB,QAAI,OAAAC,cAAY,CAAA,GAAA,CAAA,GAAA,aAAA,CAAA,KAAA,IAAA,CAAA,CAAA;AACd,OAAA,MAAA,IAAcC,YAAA,CAAA,GAAG,CAAI,EAAA;AAAuB,QAC9C,OAAAC,aAAgB,CAAA,GAAA,CAAG,CAAG;AACpB,OAAA;AAAgB,MAClB,OAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,CAAA,CAAA;AACA,KAAA;AAAc,IAChB,IAAA,cAAA,GAAA,KAAA,CAAA;AAGA,IAAAC,SAAqB,CAAA,MAAA,KAAA,CAAA,IAAA,EAAA,MAAA;AACrB,MAAA,IAAA,KAAA,CAAA,KAAA,EAAA;AAAA,cACc,IAAA,GAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,QACN,MAAA,OAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACJ,QAAA,aAAiB,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACf,QAAM,MAAA,SAAO,GAAS,IAAA,CAAA,GAAA,CAAA,IAAU,CAAA,GAAA,CAAA,IAAA,EAAA,OAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,IAAA,CAAA,CAAA;AAChC,QAAM,IAAA,SAAA,KAAU,IAAS,EAAA;AACzB,UAAM,cAAA,GAAmB,IAAA,CAAA;AAGzB,UAAM,KAAA,CAAA,aAAiB,EAAA,SAAS,CAAA,CAAA;AAEhC,SAAA;AACE,QAAiB,KAAA,CAAA,KAAA,CAAA,IAAA,GAAA,SAAA,CAAA;AACjB,QAAAC,YAAA,CAAA,oBAA8B,GAAA,KAAA,CAAA,CAAA;AAAA,OAChC;AAEA,KAAA,CAAA,CAAA;AACA,IAASD,SAAA,CAAA,MAAA;AAA8B,MACzC,IAAA,EAAA,CAAA;AAAA,MACF,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA;AAAA,KACF,EAAA,CAAA,GAAA,KAAA;AAEA,MAAA,IAAA,CAAA,cAAA,IAAA,GAAA,KAAA,KAAA,CAAA,IAAA,EAAA;AAAA,QACE,mBAAmB,EAAA,GAAA,CAAA,CAAA;AAAA,OAClB;AACC,KAAA,CAAA,CAAA;AACE,IAAAA,SAAA,CAAA,MAAM,eAAe,EAAa,CAAA,GAAA,KAAA;AAAA,MACpC,IAAA,KAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,CAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA,YACc,GAAAE,YAAA,CAAA;AAAA,MACZ,EAAS,EAAA,OAAA,CAAA,KAAA;AACP,MAAA,GAAA;AACE,MAAA,QAAA,EAAM,MAAM,QAAY,CAAA,KAAA;AAAA,MAC1B,QAAA;AAAA,MACF,GAAA,KAAA;AAAA,MACF,WAAA,EAAAC,uBAAA,CAAA,KAAA,CAAA,WAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAwB,IAAA,aACV,CAAA,MAAA,CAAA,CAAA;AAAA,IACZC,mBAAA,CAAA,MAAA,eAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAU,QAAe,KAAA;AAAA,MACzB,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAAC,YAAA,EAAA,IAAA,EAAA;AAAA,QACGC,sBAAA,CAAA,KAAA,EAAAC,cAAA,CAAA;AAAA,UACH,OAAA,EAAa,SAAe;AAAiB,UAC9C,GAAA,EAAA,OAAA;AAED,UAAA,KAAA,EAAA,CAAcC,SAAM,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAEpB,UAAgB,KAAA,EAAA,EAAA,SAAsB,EAAA,CAAA,EAAAA,SAAA,CAAA,SAAO,CAAA,CAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}